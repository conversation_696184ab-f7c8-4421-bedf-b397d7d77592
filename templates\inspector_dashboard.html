{% extends 'base.html' %}

{% block title %}لوحة تحكم المفتش{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي مع معلومات المفتش -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-tie me-2 text-primary"></i>
                لوحة تحكم المفتش
            </h1>
            <p class="text-muted mb-0">
                <i class="fas fa-user me-1"></i>
                مرحباً {{ current_user.username }}
                <span class="mx-2">|</span>
                <i class="fas fa-calendar me-1"></i>
                اليوم - {{ current_date }}
            </p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                <i class="fas fa-user-plus me-1"></i>
                إضافة أستاذ
            </button>
            <button class="btn btn-outline-info" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
        </div>
    </div>

    <!-- البطاقات الإحصائية المحدثة -->
    <div class="row mb-4">
        <!-- إجمالي الأساتذة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                الأساتذة تحت الإشراف
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ teachers|length }}</div>
                            <div class="text-xs text-muted">
                                {% if available_teachers|length > 0 %}
                                    <i class="fas fa-plus-circle text-success me-1"></i>
                                    {{ available_teachers|length }} متاح للإضافة
                                {% else %}
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    جميع الأساتذة مُعيَّنون
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نسبة الإنجاز الإجمالية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                نسبة الإنجاز الإجمالية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_completion_rate|default(0)|round|int }}%</div>
                            <div class="progress progress-sm mr-2">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ overall_completion_rate|default(0) }}%" 
                                     aria-valuenow="{{ overall_completion_rate|default(0) }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدروس المنجزة اليوم -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الدروس المنجزة اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_completed_lessons|default(0) }}</div>
                            <div class="text-xs text-muted">
                                <i class="fas fa-calendar-day me-1"></i>
                                {{ current_date }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الدروس قيد التنفيذ -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الدروس قيد التنفيذ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_in_progress_lessons|default(0) }}</div>
                            <div class="text-xs text-muted">
                                <i class="fas fa-users me-1"></i>
                                جميع الأساتذة تحت الإشراف
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأساتذة تحت الإشراف -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    الأساتذة تحت الإشراف ({{ teachers|length }})
                </h6>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أستاذ
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportTeachersData()">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if teachers %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="teachersTable">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-user me-1"></i>
                                    الأستاذ
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-chart-line me-1"></i>
                                    نسبة الإنجاز
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-tasks me-1"></i>
                                    إحصائيات التقدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-clock me-1"></i>
                                    آخر نشاط
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr class="teacher-row" data-teacher-id="{{ teacher.id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-3">
                                            <div class="avatar-title bg-primary rounded-circle">
                                                {{ teacher.username[0].upper() }}
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ teacher.username }}</h6>
                                            <small class="text-muted">أستاذ</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">{{ teacher.email }}</span>
                                </td>
                                <td>
                                    {% set completion_rate = teacher_progress[teacher.id]['completion_rate']|default(0) if teacher_progress and teacher.id in teacher_progress else 0 %}
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar 
                                                {% if completion_rate >= 80 %}bg-success
                                                {% elif completion_rate >= 60 %}bg-info  
                                                {% elif completion_rate >= 40 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ completion_rate }}%;" 
                                                aria-valuenow="{{ completion_rate }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                            </div>
                                        </div>
                                        <span class="text-sm font-weight-bold">{{ completion_rate|round|int }}%</span>
                                    </div>
                                </td>
                                <td>
                                    {% if teacher_progress and teacher.id in teacher_progress %}
                                        <div class="d-flex flex-wrap gap-1">
                                            <span class="badge bg-success" title="مكتمل">
                                                <i class="fas fa-check me-1"></i>{{ teacher_progress[teacher.id]['stats']['completed'] }}
                                            </span>
                                            <span class="badge bg-warning" title="قيد التنفيذ">
                                                <i class="fas fa-clock me-1"></i>{{ teacher_progress[teacher.id]['stats']['in_progress'] }}
                                            </span>
                                            <span class="badge bg-danger" title="مخطط">
                                                <i class="fas fa-calendar me-1"></i>{{ teacher_progress[teacher.id]['stats']['planned'] }}
                                            </span>
                                        </div>
                                        <small class="text-muted d-block mt-1">
                                            إجمالي: {{ teacher_progress[teacher.id]['stats']['total'] }} مهمة
                                        </small>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-minus me-1"></i>لا يوجد تقدم
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if teacher.updated_at %}
                                        <span class="text-muted">{{ teacher.updated_at.strftime('%Y-%m-%d') }}</span>
                                        <small class="d-block text-muted">{{ teacher.updated_at.strftime('%H:%M') }}</small>
                                    {% else %}
                                        <span class="text-muted">لا يوجد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#teacherProgressModal" 
                                                data-teacher-id="{{ teacher.id }}" 
                                                data-teacher-name="{{ teacher.username }}"
                                                title="عرض التقدم التفصيلي">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewTeacherProfile({{ teacher.id }})"
                                                title="عرض الملف الشخصي">
                                            <i class="fas fa-user"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="removeTeacher({{ teacher.id }}, '{{ teacher.username }}')"
                                                title="إزالة من الإشراف">
                                            <i class="fas fa-user-minus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد أساتذة تحت الإشراف</h5>
                    <p class="text-muted mb-4">ابدأ بإضافة أساتذة لمتابعة تقدمهم</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                        <i class="fas fa-user-plus me-1"></i>
                        إضافة أول أستاذ
                    </button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- الرسوم البيانية والإحصائيات التفصيلية -->
    <div class="row mb-4">
        <!-- رسم بياني للتقدم حسب المستوى -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقدم حسب المستوى التعليمي
                    </h6>
                </div>
                <div class="card-body">
                    {% if level_stats %}
                        <canvas id="levelProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            {% for level_id, stats in level_stats.items() %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm">{{ stats.name }}</span>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 6px;">
                                        <div class="progress-bar bg-primary" role="progressbar"
                                             style="width: {{ stats.completion_rate|default(0) }}%"></div>
                                    </div>
                                    <span class="text-sm font-weight-bold">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- رسم بياني للتقدم حسب المادة -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        التقدم حسب المادة الدراسية
                    </h6>
                </div>
                <div class="card-body">
                    {% if subject_stats %}
                        <canvas id="subjectProgressChart" width="400" height="200"></canvas>
                        <div class="mt-3">
                            <div class="row">
                                {% for subject_id, stats in subject_stats.items() %}
                                <div class="col-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                                        <span class="text-sm">{{ stats.name }}</span>
                                        <span class="text-sm font-weight-bold ms-auto">{{ stats.completion_rate|default(0)|round|int }}%</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة إضافية -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-primary text-white shadow h-100">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-white-50 small">متوسط التقدم اليومي</div>
                            <div class="text-white h5 mb-0">
                                {{ daily_average_progress|default(0)|round(1) }} مادة/يوم
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-success text-white shadow h-100">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-white-50 small">أفضل أستاذ هذا الشهر</div>
                            <div class="text-white h5 mb-0" style="min-height: 2.5rem;">
                                {% if best_teacher_this_month %}
                                    <div>{{ best_teacher_this_month.name }}</div>
                                    <small class="d-block text-white-75">{{ best_teacher_this_month.completed_count }} درس</small>
                                {% else %}
                                    <div>لا يوجد بيانات</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card bg-gradient-info text-white shadow h-100">
                <div class="card-body d-flex flex-column justify-content-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-white-50 small">الإشعارات غير المقروءة</div>
                            <div class="text-white h5 mb-0">
                                {{ unread_notifications_count|default(0) }}
                            </div>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}
