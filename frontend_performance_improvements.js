/**
 * تحسينات الأداء للواجهة الأمامية
 * هذا الملف يحتوي على تحسينات JavaScript لتحسين تجربة المستخدم
 */

// تحميل البيانات بشكل تدريجي
class DashboardLoader {
    constructor() {
        this.loadingSteps = [
            { name: 'basic', element: '.basic-stats', delay: 0 },
            { name: 'charts', element: '.charts-container', delay: 500 },
            { name: 'tables', element: '.tables-container', delay: 1000 },
            { name: 'details', element: '.details-container', delay: 1500 }
        ];
        this.currentStep = 0;
    }

    init() {
        this.showLoadingIndicator();
        this.loadNextStep();
    }

    showLoadingIndicator() {
        // إظهار مؤشر التحميل
        const loader = document.createElement('div');
        loader.id = 'dashboard-loader';
        loader.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل لوحة التحكم...</p>
                <div class="progress mt-2">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(loader, container.firstChild);
        }
    }

    loadNextStep() {
        if (this.currentStep >= this.loadingSteps.length) {
            this.hideLoadingIndicator();
            return;
        }

        const step = this.loadingSteps[this.currentStep];
        const progress = ((this.currentStep + 1) / this.loadingSteps.length) * 100;
        
        // تحديث شريط التقدم
        const progressBar = document.querySelector('#dashboard-loader .progress-bar');
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }

        setTimeout(() => {
            this.showElement(step.element);
            this.currentStep++;
            this.loadNextStep();
        }, step.delay);
    }

    showElement(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.display = 'block';
            
            // تأثير الظهور التدريجي
            setTimeout(() => {
                element.style.transition = 'opacity 0.5s ease-in-out';
                element.style.opacity = '1';
            }, 50);
        });
    }

    hideLoadingIndicator() {
        const loader = document.getElementById('dashboard-loader');
        if (loader) {
            loader.style.transition = 'opacity 0.3s ease-out';
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
        }
    }
}

// تحسين تحميل الرسوم البيانية
class ChartOptimizer {
    constructor() {
        this.charts = [];
        this.observer = null;
    }

    init() {
        // تحميل الرسوم البيانية عند الحاجة فقط
        this.setupIntersectionObserver();
        this.optimizeChartRendering();
    }

    setupIntersectionObserver() {
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadChart(entry.target);
                    this.observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        // مراقبة عناصر الرسوم البيانية
        document.querySelectorAll('.chart-container').forEach(chart => {
            this.observer.observe(chart);
        });
    }

    loadChart(container) {
        const chartType = container.dataset.chartType;
        const chartData = JSON.parse(container.dataset.chartData || '{}');
        
        switch (chartType) {
            case 'progress':
                this.createProgressChart(container, chartData);
                break;
            case 'level':
                this.createLevelChart(container, chartData);
                break;
            default:
                console.warn('نوع رسم بياني غير معروف:', chartType);
        }
    }

    createProgressChart(container, data) {
        // إنشاء رسم بياني للتقدم
        const canvas = container.querySelector('canvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });

        this.charts.push(chart);
    }

    createLevelChart(container, data) {
        // إنشاء رسم بياني للمستويات
        const canvas = container.querySelector('canvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1200,
                    easing: 'easeOutBounce'
                }
            }
        });

        this.charts.push(chart);
    }

    optimizeChartRendering() {
        // تحسين أداء الرسوم البيانية
        Chart.defaults.animation.duration = 800;
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
    }
}

// تحسين الجداول الكبيرة
class TableOptimizer {
    constructor() {
        this.pageSize = 10;
        this.currentPage = 1;
    }

    init() {
        this.setupPagination();
        this.setupSearch();
        this.setupSorting();
    }

    setupPagination() {
        const tables = document.querySelectorAll('.large-table');
        tables.forEach(table => {
            this.paginateTable(table);
        });
    }

    paginateTable(table) {
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const totalPages = Math.ceil(rows.length / this.pageSize);
        
        if (totalPages <= 1) return;

        // إخفاء جميع الصفوف
        rows.forEach(row => row.style.display = 'none');
        
        // إظهار الصفحة الأولى
        this.showPage(table, 1);
        
        // إنشاء أزرار التنقل
        this.createPaginationControls(table, totalPages);
    }

    showPage(table, pageNumber) {
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const startIndex = (pageNumber - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        
        rows.forEach((row, index) => {
            if (index >= startIndex && index < endIndex) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    createPaginationControls(table, totalPages) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'pagination-container mt-3';
        
        for (let i = 1; i <= totalPages; i++) {
            const button = document.createElement('button');
            button.className = 'btn btn-sm btn-outline-primary mx-1';
            button.textContent = i;
            button.onclick = () => {
                this.showPage(table, i);
                this.updateActiveButton(paginationContainer, button);
            };
            
            if (i === 1) button.classList.add('active');
            paginationContainer.appendChild(button);
        }
        
        table.parentNode.appendChild(paginationContainer);
    }

    updateActiveButton(container, activeButton) {
        container.querySelectorAll('button').forEach(btn => {
            btn.classList.remove('active');
        });
        activeButton.classList.add('active');
    }

    setupSearch() {
        // إضافة وظيفة البحث للجداول
        const searchInputs = document.querySelectorAll('.table-search');
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.filterTable(e.target);
            });
        });
    }

    filterTable(searchInput) {
        const table = searchInput.closest('.table-container').querySelector('table');
        const rows = table.querySelectorAll('tbody tr');
        const searchTerm = searchInput.value.toLowerCase();
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    setupSorting() {
        // إضافة وظيفة الترتيب للجداول
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(header);
            });
        });
    }

    sortTable(header) {
        const table = header.closest('table');
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        
        const isAscending = !header.classList.contains('sort-asc');
        
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aText.localeCompare(bText, 'ar');
            } else {
                return bText.localeCompare(aText, 'ar');
            }
        });
        
        // إعادة ترتيب الصفوف
        const tbody = table.querySelector('tbody');
        rows.forEach(row => tbody.appendChild(row));
        
        // تحديث مؤشر الترتيب
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    }
}

// تهيئة التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من أن الصفحة هي لوحة تحكم المفتش
    if (window.location.pathname.includes('/dashboard/inspector')) {
        const loader = new DashboardLoader();
        const chartOptimizer = new ChartOptimizer();
        const tableOptimizer = new TableOptimizer();
        
        // بدء التحميل التدريجي
        loader.init();
        
        // تهيئة التحسينات الأخرى بعد التحميل
        setTimeout(() => {
            chartOptimizer.init();
            tableOptimizer.init();
        }, 2000);
    }
});

// تحسين الذاكرة عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    // تنظيف الرسوم البيانية
    if (window.Chart && Chart.instances) {
        Object.values(Chart.instances).forEach(chart => {
            chart.destroy();
        });
    }
    
    // تنظيف المراقبين
    if (window.observer) {
        window.observer.disconnect();
    }
});
