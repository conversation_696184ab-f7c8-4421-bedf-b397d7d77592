"""
تحسينات إضافية لأداء قاعدة البيانات
هذا الملف يحتوي على تحسينات إضافية يمكن تطبيقها لتحسين الأداء أكثر
"""

from app import app, db
from sqlalchemy import text

def create_database_indexes():
    """
    إنشاء فهارس لتحسين أداء الاستعلامات
    """
    try:
        with app.app_context():
            # فهرس لجدول ProgressEntry
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_progress_entry_user_status 
                ON progress_entry(user_id, status)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_progress_entry_date 
                ON progress_entry(date)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_progress_entry_material 
                ON progress_entry(material_id)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_progress_entry_user_date 
                ON progress_entry(user_id, date)
            """))
            
            # فهرس لجدول Schedule
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_schedule_user_level 
                ON schedule(user_id, level_id)
            """))
            
            # فهرس لجدول LevelDataEntry
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_level_data_entry_db_type_parent 
                ON level_data_entry(database_id, entry_type, parent_id)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_level_data_entry_active 
                ON level_data_entry(is_active)
            """))
            
            # فهرس لجدول الإشعارات
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_admin_inspector_notification_receiver_read 
                ON admin_inspector_notification(receiver_id, is_read)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_inspector_teacher_notification_receiver_read 
                ON inspector_teacher_notification(receiver_id, is_read)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_general_notification_read_user_notification 
                ON general_notification_read(user_id, notification_id)
            """))
            
            # فهرس لجدول inspector_teacher
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_inspector_teacher_inspector 
                ON inspector_teacher(inspector_id)
            """))
            
            db.session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_inspector_teacher_teacher 
                ON inspector_teacher(teacher_id)
            """))
            
            db.session.commit()
            print("تم إنشاء جميع الفهارس بنجاح")
            
    except Exception as e:
        print(f"خطأ في إنشاء الفهارس: {str(e)}")
        db.session.rollback()

def optimize_database_settings():
    """
    تحسين إعدادات قاعدة البيانات
    """
    try:
        with app.app_context():
            # تحسين إعدادات SQLite
            db.session.execute(text("PRAGMA journal_mode = WAL"))
            db.session.execute(text("PRAGMA synchronous = NORMAL"))
            db.session.execute(text("PRAGMA cache_size = 10000"))
            db.session.execute(text("PRAGMA temp_store = MEMORY"))
            db.session.execute(text("PRAGMA mmap_size = 268435456"))  # 256MB
            
            db.session.commit()
            print("تم تحسين إعدادات قاعدة البيانات")
            
    except Exception as e:
        print(f"خطأ في تحسين إعدادات قاعدة البيانات: {str(e)}")

def analyze_database_performance():
    """
    تحليل أداء قاعدة البيانات
    """
    try:
        with app.app_context():
            # تحليل الجداول
            tables = ['progress_entry', 'schedule', 'level_data_entry', 'user']
            
            for table in tables:
                result = db.session.execute(text(f"ANALYZE {table}")).fetchall()
                print(f"تم تحليل جدول {table}")
            
            # إحصائيات قاعدة البيانات
            stats = db.session.execute(text("""
                SELECT name, sql FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)).fetchall()
            
            print(f"عدد الفهارس المخصصة: {len(stats)}")
            for stat in stats:
                print(f"- {stat[0]}")
                
    except Exception as e:
        print(f"خطأ في تحليل الأداء: {str(e)}")

def cleanup_old_data():
    """
    تنظيف البيانات القديمة لتحسين الأداء
    """
    try:
        with app.app_context():
            from datetime import datetime, timedelta
            
            # حذف سجلات التقدم القديمة جداً (أكثر من سنتين)
            two_years_ago = datetime.now() - timedelta(days=730)
            
            old_entries = db.session.execute(text("""
                DELETE FROM progress_entry 
                WHERE date < :date AND status = 'planned'
            """), {'date': two_years_ago.date()}).rowcount
            
            print(f"تم حذف {old_entries} سجل تقدم قديم")
            
            # تنظيف الإشعارات القديمة المقروءة (أكثر من 6 أشهر)
            six_months_ago = datetime.now() - timedelta(days=180)
            
            old_notifications = db.session.execute(text("""
                DELETE FROM admin_inspector_notification 
                WHERE created_at < :date AND is_read = 1
            """), {'date': six_months_ago}).rowcount
            
            old_notifications += db.session.execute(text("""
                DELETE FROM inspector_teacher_notification 
                WHERE created_at < :date AND is_read = 1
            """), {'date': six_months_ago}).rowcount
            
            print(f"تم حذف {old_notifications} إشعار قديم")
            
            db.session.commit()
            
    except Exception as e:
        print(f"خطأ في تنظيف البيانات: {str(e)}")
        db.session.rollback()

if __name__ == "__main__":
    print("بدء تحسينات قاعدة البيانات...")
    
    # إنشاء الفهارس
    create_database_indexes()
    
    # تحسين الإعدادات
    optimize_database_settings()
    
    # تحليل الأداء
    analyze_database_performance()
    
    # تنظيف البيانات القديمة (اختياري)
    # cleanup_old_data()
    
    print("انتهت تحسينات قاعدة البيانات")
