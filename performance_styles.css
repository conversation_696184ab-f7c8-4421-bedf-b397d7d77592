/* تحسينات الأداء البصري للوحة تحكم المفتش */

/* تحسين التحميل التدريجي */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* مؤشر التحميل المحسن */
#dashboard-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

#dashboard-loader .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.chart-container.loaded {
    opacity: 1;
}

.chart-container canvas {
    max-height: 100%;
    width: 100% !important;
    height: auto !important;
}

/* تحسين الجداول */
.table-responsive {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.table-responsive::-webkit-scrollbar {
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسين أداء الجداول الكبيرة */
.large-table {
    table-layout: fixed;
}

.large-table th,
.large-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* أزرار الترقيم */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.pagination-container button {
    min-width: 40px;
    height: 35px;
}

.pagination-container button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* تحسين البطاقات */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    will-change: transform;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease-in-out;
    will-change: transform, background-color;
}

.btn:hover {
    transform: translateY(-1px);
}

/* تحسين شريط التقدم */
.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
    background: linear-gradient(45deg, #007bff, #0056b3);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.stats-card:hover::before {
    left: 100%;
}

/* تحسين النصوص */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تحسين الأيقونات */
.icon-lg {
    font-size: 2rem;
    opacity: 0.8;
}

.icon-animated {
    transition: transform 0.3s ease;
}

.icon-animated:hover {
    transform: scale(1.1) rotate(5deg);
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-dismissible .btn-close {
    padding: 0.75rem;
}

/* تحسين النماذج */
.form-control {
    border-radius: 6px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.dropdown-item {
    transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* تحسين الشبكة */
.row {
    margin-left: -7.5px;
    margin-right: -7.5px;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto, .col-sm, .col-sm-1, .col-sm-2, .col-sm-3,
.col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8,
.col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-auto,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4,
.col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9,
.col-md-10, .col-md-11, .col-md-12, .col-md-auto,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4,
.col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9,
.col-lg-10, .col-lg-11, .col-lg-12, .col-lg-auto,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4,
.col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9,
.col-xl-10, .col-xl-11, .col-xl-12, .col-xl-auto {
    padding-left: 7.5px;
    padding-right: 7.5px;
}

/* تحسين الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }
    
    .table-responsive {
        max-height: 300px;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
}

/* تحسين الطباعة */
@media print {
    .btn, .pagination-container, .dropdown {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 12px;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* تحسين إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* تحسين الحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
