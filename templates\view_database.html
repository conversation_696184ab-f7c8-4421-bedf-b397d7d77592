{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">
            قاعدة بيانات: {{ database.name }} ({{ database.level.name }})
            <div class="float-end">
                <button class="btn btn-primary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#importDatabaseModal">
                    <i class="fas fa-file-import me-1"></i> استيراد من Excel
                </button>
                <a href="{{ url_for('export_database_data', db_id=database.id) }}" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-file-export me-1"></i> تصدير إلى Excel
                </a>
                <a href="{{ url_for('manage_databases') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i> العودة
                </a>
            </div>
        </h2>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="databaseTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab" aria-controls="subjects" aria-selected="true">المواد الدراسية</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="domains-tab" data-bs-toggle="tab" data-bs-target="#domains" type="button" role="tab" aria-controls="domains" aria-selected="false">الميادين</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">المواد المعرفية</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="competencies-tab" data-bs-toggle="tab" data-bs-target="#competencies" type="button" role="tab" aria-controls="competencies" aria-selected="false">الكفاءات المستهدفة</button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="databaseTabsContent">
                    <!-- المواد الدراسية -->
                    <div class="tab-pane fade show active" id="subjects" role="tabpanel" aria-labelledby="subjects-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>المواد الدراسية</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='subject') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-primary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#addEntryModal" data-type="subject" data-parent-id="0">
                                    <i class="fas fa-plus me-1"></i> إضافة مادة
                                </button>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="subject" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المادة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'subject' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="domain" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> ميدان
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- الميادين -->
                    <div class="tab-pane fade" id="domains" role="tabpanel" aria-labelledby="domains-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>الميادين</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='domain') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="domain" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>المادة</th>
                                        <th>اسم الميدان</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'domain' %}
                                    <tr data-type="domain" data-id="{{ entry.id }}" data-parent-id="{{ entry.parent_id or '0' }}" data-name="{{ entry.name }}">
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعل</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطل</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="material" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> مادة معرفية
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- المواد المعرفية -->
                    <div class="tab-pane fade" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>المواد المعرفية</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='material') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="material" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الميدان</th>
                                        <th>اسم المادة المعرفية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'material' %}
                                    <tr data-type="material" data-id="{{ entry.id }}" data-parent-id="{{ entry.parent_id or '0' }}" data-name="{{ entry.name }}">
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="competency" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> كفاءة
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- الكفاءات المستهدفة -->
                    <div class="tab-pane fade" id="competencies" role="tabpanel" aria-labelledby="competencies-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>الكفاءات المستهدفة</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='competency') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="competency" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>المادة المعرفية</th>
                                        <th>وصف الكفاءة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'competency' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.description }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Entry Modal -->
<div class="modal fade" id="addEntryModal" tabindex="-1" aria-labelledby="addEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEntryModalLabel">إضافة عنصر جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addEntryForm" method="POST" action="{{ url_for('add_database_entry', db_id=database.id) }}">
                <div class="modal-body">
                    <input type="hidden" id="entry_type" name="entry_type" value="">
                    <input type="hidden" id="parent_id" name="parent_id" value="0">

                    <div class="mb-3">
                        <label for="entry_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="entry_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="entry_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="entry_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="entry_is_active" name="is_active" checked>
                        <label class="form-check-label" for="entry_is_active">
                            تفعيل العنصر
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Entry Modal -->
<div class="modal fade" id="editEntryModal" tabindex="-1" aria-labelledby="editEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEntryModalLabel">تعديل عنصر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editEntryForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_entry_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="edit_entry_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_entry_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_entry_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_entry_is_active" name="is_active">
                        <label class="form-check-label" for="edit_entry_is_active">
                            تفعيل العنصر
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Entry Confirmation Modal -->
<div class="modal fade" id="deleteEntryModal" tabindex="-1" aria-labelledby="deleteEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEntryModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العنصر؟ سيتم حذف جميع العناصر المرتبطة به أيضاً.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteEntryForm" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Entry Modal
        const addEntryModal = document.getElementById('addEntryModal');
        const entryTypeInput = document.getElementById('entry_type');
        const parentIdInput = document.getElementById('parent_id');
        const addEntryModalTitle = document.getElementById('addEntryModalLabel');

        addEntryModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const type = button.getAttribute('data-type');
            const parentId = button.getAttribute('data-parent-id');
            const parentName = button.getAttribute('data-name');

            entryTypeInput.value = type;
            parentIdInput.value = parentId;

            let title = '';
            switch(type) {
                case 'subject':
                    title = 'إضافة مادة دراسية جديدة';
                    break;
                case 'domain':
                    title = `إضافة ميدان جديد للمادة: ${parentName}`;
                    break;
                case 'material':
                    title = `إضافة مادة معرفية جديدة للميدان: ${parentName}`;
                    break;
                case 'competency':
                    title = `إضافة كفاءة مستهدفة جديدة للمادة المعرفية: ${parentName}`;
                    break;
            }

            addEntryModalTitle.textContent = title;
        });

        // Edit Entry Modal
        const editEntryModal = document.getElementById('editEntryModal');
        const editEntryForm = document.getElementById('editEntryForm');
        const editEntryName = document.getElementById('edit_entry_name');
        const editEntryDescription = document.getElementById('edit_entry_description');
        const editEntryIsActive = document.getElementById('edit_entry_is_active');

        document.querySelectorAll('.edit-entry').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');
                const isActive = this.getAttribute('data-is-active') === 'True';

                editEntryForm.action = "{{ url_for('edit_database_entry', db_id=database.id, entry_id=0) }}".replace('0', id);
                editEntryName.value = name;
                editEntryDescription.value = description;
                editEntryIsActive.checked = isActive;

                const modal = new bootstrap.Modal(editEntryModal);
                modal.show();
            });
        });

        // Add Child Entry
        document.querySelectorAll('.add-child').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');
                const name = this.getAttribute('data-name');

                entryTypeInput.value = type;
                parentIdInput.value = id;

                let title = '';
                switch(type) {
                    case 'domain':
                        title = `إضافة ميدان جديد للمادة: ${name}`;
                        break;
                    case 'material':
                        title = `إضافة مادة معرفية جديدة للميدان: ${name}`;
                        break;
                    case 'competency':
                        title = `إضافة كفاءة مستهدفة جديدة للمادة المعرفية: ${name}`;
                        break;
                }

                addEntryModalTitle.textContent = title;

                const modal = new bootstrap.Modal(addEntryModal);
                modal.show();
            });
        });

        // Delete Entry
        const deleteEntryModal = document.getElementById('deleteEntryModal');
        const deleteEntryForm = document.getElementById('deleteEntryForm');

        document.querySelectorAll('.delete-entry').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');

                deleteEntryForm.action = "{{ url_for('delete_database_entry', db_id=database.id, entry_id=0) }}".replace('0', id);

                const modal = new bootstrap.Modal(deleteEntryModal);
                modal.show();
            });
        });
    });
</script>
{% endblock %}

<!-- Import Database Modal -->
<div class="modal fade" id="importDatabaseModal" tabindex="-1" aria-labelledby="importDatabaseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importDatabaseModalLabel">استيراد بيانات من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="importDatabaseForm" method="POST" action="{{ url_for('import_database_data', db_id=database.id) }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_type" class="form-label">نوع البيانات</label>
                        <select class="form-select" id="import_type" name="import_type" required>
                            <option value="all" selected>جميع البيانات</option>
                            <option value="subject">المواد الدراسية</option>
                            <option value="domain">الميادين</option>
                            <option value="material">المواد المعرفية</option>
                            <option value="competency">الكفاءات المستهدفة</option>
                            <option value="hierarchical">البيانات المترابطة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="import_file" class="form-label">ملف Excel</label>
                        <input type="file" class="form-control" id="import_file" name="file" accept=".xlsx" required>
                        <div class="form-text">يجب أن يكون الملف بصيغة Excel (.xlsx) ويحتوي على الأعمدة المطلوبة.</div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="clear_existing" name="clear_existing">
                        <label class="form-check-label" for="clear_existing">
                            حذف البيانات الحالية قبل الاستيراد
                        </label>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i> تنبيه: الرجاء التأكد من أن الملف يحتوي على البيانات بالتنسيق الصحيح. استخدم ملفات تم تصديرها من النظام كنموذج.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Add Multiple Entries Modal -->
<div class="modal fade" id="addMultipleEntriesModal" tabindex="-1" aria-labelledby="addMultipleEntriesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMultipleEntriesModalLabel">إضافة عناصر متعددة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addMultipleEntriesForm" method="POST" action="{{ url_for('add_database_entry', db_id=database.id) }}">
                <div class="modal-body">
                    <input type="hidden" id="multiple_entry_type" name="entry_type" value="">
                    <input type="hidden" id="multiple_parent_id" name="parent_id" value="0">
                    <input type="hidden" name="is_multiple" value="true">

                    <!-- أداة الفلترة حسب الميدان (الأولوية الأولى) -->
                    <div class="mb-3" id="domain_filter_container" style="display: block;">
                        <label for="domain_filter" class="form-label">
                            <i class="fas fa-filter me-1"></i>
                            <strong>الخطوة 1:</strong> فلترة حسب الميدان
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-layer-group"></i>
                            </span>
                            <select class="form-select" id="domain_filter" onchange="filterByDomain()">
                                <option value="">جميع الميادين</option>
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </select>
                            <button class="btn btn-outline-info" type="button" onclick="clearDomainFilter()" title="مسح الفلترة">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            <strong>ابدأ هنا:</strong> اختر ميدان لعرض المواد المعرفية المتعلقة به فقط
                        </small>
                    </div>

                    <div class="mb-3" id="parent_selector_container">

                        <!-- خانة البحث (الخطوة الثانية) -->
                        <div class="mb-2">
                            <label for="parent_search" class="form-label">
                                <i class="fas fa-search me-1"></i>
                                <strong>الخطوة 2:</strong> البحث النصي (اختياري)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="parent_search"
                                       placeholder="ابحث داخل النتائج المفلترة..."
                                       onkeyup="filterParentOptions()">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearParentSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                اكتب للبحث السريع داخل المواد المعرفية المفلترة
                            </small>
                        </div>

                        <!-- قائمة العناصر الأب (اختيار متعدد) -->
                        <label class="form-label">
                            <i class="fas fa-list-check me-1"></i>
                            <strong>الخطوة 3:</strong> اختيار العناصر الأب (متعدد)
                        </label>

                        <!-- أزرار التحكم السريع -->
                        <div class="mb-2">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="selectAllParents()">
                                    <i class="fas fa-check-double me-1"></i>
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="clearAllParents()">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء الكل
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="toggleParentSelection()">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    عكس التحديد
                                </button>
                            </div>
                            <span class="ms-3 text-muted small">
                                <span id="selected_count">0</span> عنصر محدد
                            </span>
                        </div>

                        <!-- قائمة العناصر مع checkboxes -->
                        <div class="border rounded" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                            <div id="parent_checkboxes_container" class="p-2">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                                    <p>اختر ميدان من الخطوة 1 لعرض المواد المعرفية</p>
                                </div>
                            </div>
                        </div>

                        <!-- حقل مخفي لتخزين العناصر المحددة -->
                        <input type="hidden" id="selected_parents" name="selected_parents" value="">

                        <!-- رسالة عدم وجود نتائج -->
                        <div id="no_results_message" class="alert alert-warning mt-3" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle fa-2x me-3 text-warning"></i>
                                <div>
                                    <h6 class="alert-heading mb-1">لا توجد نتائج مطابقة</h6>
                                    <p class="mb-2">لم يتم العثور على مواد معرفية تطابق معايير البحث والفلترة</p>
                                    <div class="small">
                                        <strong>اقتراحات:</strong>
                                        <ul class="mb-0 mt-1">
                                            <li>جرب اختيار ميدان آخر من الخطوة 1</li>
                                            <li>امسح البحث النصي من الخطوة 2</li>
                                            <li>تأكد من وجود مواد معرفية في الميدان المختار</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- عداد النتائج المحسن -->
                        <div class="mt-3 p-2 bg-light rounded">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <i class="fas fa-chart-bar text-primary me-1"></i>
                                    <strong>النتائج:</strong>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary" id="parent_count">0</span>
                                    <small class="text-muted">عنصر متاح</small>
                                </div>
                            </div>

                            <div id="filter_status" class="mt-2" style="display: none;">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span id="filtered_count" style="display: none;">
                                        <i class="fas fa-search text-info me-1"></i>
                                        <span class="badge bg-info" id="filtered_number">0</span>
                                        <small class="text-muted">مطابق للبحث</small>
                                    </span>
                                    <span id="domain_filtered_info" style="display: none;">
                                        <i class="fas fa-filter text-warning me-1"></i>
                                        <span class="badge bg-warning text-dark">مفلتر حسب الميدان</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="multiple_entries" class="form-label">العناصر (كل عنصر في سطر جديد)</label>
                        <textarea class="form-control" id="multiple_entries" name="multiple_entries" rows="10" placeholder="أدخل كل عنصر في سطر جديد" required></textarea>
                        <div class="form-text">أدخل كل عنصر في سطر جديد. يمكنك لصق قائمة من Excel أو أي مصدر آخر.</div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="multiple_is_active" name="is_active" checked>
                        <label class="form-check-label" for="multiple_is_active">
                            مفعل
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // تحديث مودال الإضافة المتعددة
    document.addEventListener('DOMContentLoaded', function() {
        const addMultipleEntriesModal = document.getElementById('addMultipleEntriesModal');
        if (addMultipleEntriesModal) {
            addMultipleEntriesModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const type = button.getAttribute('data-type');
                const parentId = button.getAttribute('data-parent-id');
                const parentName = button.getAttribute('data-name');

                // تحديث العنوان
                const modalTitle = addMultipleEntriesModal.querySelector('.modal-title');
                let title = 'إضافة عناصر متعددة';

                if (type === 'subject') {
                    title = 'إضافة مواد دراسية متعددة';
                } else if (type === 'domain') {
                    title = 'إضافة ميادين متعددة';
                    if (parentName) {
                        title += ' للمادة: ' + parentName;
                    }
                } else if (type === 'material') {
                    title = 'إضافة مواد معرفية متعددة';
                    if (parentName) {
                        title += ' للميدان: ' + parentName;
                    }
                } else if (type === 'competency') {
                    title = 'إضافة كفاءات متعددة';
                    if (parentName) {
                        title += ' للمادة المعرفية: ' + parentName;
                    }
                }

                modalTitle.textContent = title;

                // تحديث الحقول المخفية
                document.getElementById('multiple_entry_type').value = type;
                document.getElementById('multiple_parent_id').value = parentId;

                // إضافة تسجيل للتشخيص
                console.log('=== تشخيص النافذة المنبثقة ===');
                console.log('نوع العنصر:', type);
                console.log('معرف العنصر الأب:', parentId);
                console.log('اسم العنصر الأب:', parentName);

                // تحديث قائمة العناصر الأب
                const parentSelector = document.getElementById('parent_selector');
                parentSelector.innerHTML = '<option value="0">اختر العنصر الأب</option>';

                // إظهار/إخفاء قائمة العناصر الأب حسب النوع
                const parentSelectorContainer = document.getElementById('parent_selector_container');
                const domainFilterContainer = document.getElementById('domain_filter_container');

                console.log('عنصر حاوي العناصر الأب:', parentSelectorContainer);
                console.log('عنصر حاوي فلتر الميدان:', domainFilterContainer);

                // إظهار/إخفاء العناصر حسب النوع
                if (type === 'subject') {
                    // لا تحتاج المواد الدراسية إلى عنصر أب أو فلتر ميدان
                    console.log('نوع المادة الدراسية - إخفاء جميع العناصر');
                    parentSelectorContainer.style.display = 'none';
                    domainFilterContainer.style.display = 'none';
                } else if (type === 'competency') {
                    // الكفاءات تحتاج إلى فلتر الميدان وقائمة العناصر الأب
                    console.log('نوع الكفاءة - إظهار فلتر الميدان وحاوي العناصر');
                    parentSelectorContainer.style.display = 'block';
                    domainFilterContainer.style.display = 'block';

                    // تأخير صغير لضمان تحميل الصفحة
                    setTimeout(() => {
                        loadDomainFilter();
                        console.log('تم استدعاء loadDomainFilter() مع تأخير');
                    }, 100);
                } else {
                    // الأنواع الأخرى تحتاج فقط إلى قائمة العناصر الأب
                    console.log('نوع آخر - إظهار حاوي العناصر فقط');
                    parentSelectorContainer.style.display = 'block';
                    domainFilterContainer.style.display = 'none';
                }

                    // تحميل العناصر الأب المناسبة
                    let parentType = '';
                    if (type === 'domain') parentType = 'subject';
                    else if (type === 'material') parentType = 'domain';
                    else if (type === 'competency') parentType = 'material';

                    // تحميل العناصر الأب من الصفحة
                    const entries = document.querySelectorAll(`tr[data-type="${parentType}"]`);
                    entries.forEach(entry => {
                        const entryId = entry.getAttribute('data-id');
                        const entryName = entry.getAttribute('data-name');
                        if (entryId && entryName) {
                            const option = document.createElement('option');
                            option.value = entryId;
                            option.textContent = entryName;
                            parentSelector.appendChild(option);
                        }
                    });

                    // إذا تم تحديد عنصر أب
                    if (parentId && parentId !== '0') {
                        parentSelector.value = parentId;
                    }

                    // تحديث عداد العناصر
                    updateParentCount();
                }
            });

            // تحديث العنصر الأب عند تغيير الاختيار
            const parentSelector = document.getElementById('parent_selector');
            parentSelector.addEventListener('change', function() {
                document.getElementById('multiple_parent_id').value = this.value;
            });

            // فحص نهائي للتأكد من ظهور أداة الفلترة للكفاءات
            const currentType = document.getElementById('multiple_entry_type').value;
            console.log('=== فحص نهائي ===');
            console.log('النوع الحالي:', currentType);
            console.log('حالة عرض فلتر الميدان:', domainFilterContainer.style.display);

            if (currentType === 'competency') {
                if (domainFilterContainer.style.display === 'none') {
                    console.log('إصلاح: إظهار فلتر الميدان للكفاءات');
                    domainFilterContainer.style.display = 'block';
                }

                // تأكد من تحميل الميادين
                if (typeof loadDomainFilter === 'function') {
                    setTimeout(() => {
                        loadDomainFilter();
                        console.log('تم استدعاء loadDomainFilter() في الفحص النهائي');
                    }, 200);
                }
            }

            // تقديم نموذج الإضافة المتعددة
            const addMultipleEntriesForm = document.getElementById('addMultipleEntriesForm');
            addMultipleEntriesForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(this);
                const entriesText = formData.get('multiple_entries');
                const entries = entriesText.split('\n').filter(entry => entry.trim() !== '');
                const selectedParents = document.getElementById('selected_parents').value;

                // التحقق من وجود كفاءات للإضافة
                if (entries.length === 0) {
                    alert('الرجاء إدخال كفاءة واحدة على الأقل');
                    return;
                }

                // التحقق من تحديد عناصر أب
                if (!selectedParents || selectedParents.trim() === '') {
                    alert('الرجاء تحديد عنصر أب واحد على الأقل من الخطوة 3');
                    return;
                }

                const selectedParentIds = selectedParents.split(',').filter(id => id.trim() !== '');

                // تأكيد العملية
                const confirmMessage = `هل أنت متأكد من إضافة ${entries.length} كفاءة إلى ${selectedParentIds.length} مادة معرفية؟\n\nسيتم إنشاء ${entries.length * selectedParentIds.length} كفاءة إجمالية.`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                // إرسال البيانات لكل عنصر أب محدد
                let completedRequests = 0;
                let totalRequests = selectedParentIds.length;
                let successCount = 0;
                let errorMessages = [];

                selectedParentIds.forEach((parentId, index) => {
                    // إنشاء FormData جديد لكل عنصر أب
                    const individualFormData = new FormData();
                    individualFormData.append('entry_type', formData.get('entry_type'));
                    individualFormData.append('parent_id', parentId.trim());
                    individualFormData.append('multiple_entries', entriesText);
                    individualFormData.append('is_multiple', 'true');
                    individualFormData.append('is_active', formData.get('is_active') || 'on');

                    // إرسال الطلب
                    fetch(this.action, {
                        method: 'POST',
                        body: individualFormData
                    })
                    .then(response => response.json())
                    .then(data => {
                        completedRequests++;

                        if (data.success) {
                            successCount++;
                            console.log(`تم إضافة الكفاءات للعنصر الأب ${parentId} بنجاح`);
                        } else {
                            errorMessages.push(`العنصر ${parentId}: ${data.message}`);
                        }

                        // إذا تم الانتهاء من جميع الطلبات
                        if (completedRequests === totalRequests) {
                            if (successCount === totalRequests) {
                                alert(`تم بنجاح! تم إضافة ${entries.length} كفاءة إلى ${successCount} مادة معرفية.\n\nإجمالي الكفاءات المضافة: ${entries.length * successCount}`);
                                window.location.reload();
                            } else {
                                let message = `تم إضافة الكفاءات إلى ${successCount} من أصل ${totalRequests} مادة معرفية.`;
                                if (errorMessages.length > 0) {
                                    message += '\n\nالأخطاء:\n' + errorMessages.join('\n');
                                }
                                alert(message);
                                window.location.reload();
                            }
                        }
                    })
                    .catch(error => {
                        completedRequests++;
                        errorMessages.push(`العنصر ${parentId}: خطأ في الشبكة`);
                        console.error(`Error for parent ${parentId}:`, error);

                        // إذا تم الانتهاء من جميع الطلبات
                        if (completedRequests === totalRequests) {
                            let message = `تم إضافة الكفاءات إلى ${successCount} من أصل ${totalRequests} مادة معرفية.`;
                            if (errorMessages.length > 0) {
                                message += '\n\nالأخطاء:\n' + errorMessages.join('\n');
                            }
                            alert(message);
                            window.location.reload();
                        }
                    });
                });
            });
        }
    });

    // دوال البحث والتصفية للعناصر الأب

    // تحميل قائمة الميادين للفلترة
    function loadDomainFilter() {
        console.log('=== بدء تحميل فلتر الميدان ===');
        const domainFilter = document.getElementById('domain_filter');
        console.log('عنصر فلتر الميدان:', domainFilter);

        if (!domainFilter) {
            console.error('لم يتم العثور على عنصر فلتر الميدان!');
            return;
        }

        // مسح المحتوى الحالي
        domainFilter.innerHTML = '<option value="">جميع الميادين</option>';

        // البحث عن جميع الميادين في الصفحة
        const domainEntries = document.querySelectorAll('tr[data-type="domain"]');
        console.log('عدد الميادين الموجودة في الصفحة:', domainEntries.length);

        // طباعة تفاصيل الميادين الموجودة
        domainEntries.forEach((entry, index) => {
            const domainId = entry.getAttribute('data-id');
            const domainName = entry.getAttribute('data-name');
            console.log(`الميدان ${index + 1}: ID=${domainId}, Name=${domainName}`);
        });

        const domains = new Set(); // لتجنب التكرار

        domainEntries.forEach(entry => {
            const domainId = entry.getAttribute('data-id');
            const domainName = entry.getAttribute('data-name');
            if (domainId && domainName && !domains.has(domainId)) {
                domains.add(domainId);
                const option = document.createElement('option');
                option.value = domainId;
                option.textContent = domainName;
                option.setAttribute('data-domain-name', domainName);
                domainFilter.appendChild(option);
                console.log(`تمت إضافة الميدان: ${domainName} (ID: ${domainId})`);
            }
        });

        console.log(`تم تحميل ${domains.size} ميدان للفلترة`);

        // إذا لم توجد ميادين، أضف رسالة توضيحية
        if (domains.size === 0) {
            const noDomainsOption = document.createElement('option');
            noDomainsOption.value = '';
            noDomainsOption.textContent = 'لا توجد ميادين متاحة';
            noDomainsOption.disabled = true;
            domainFilter.appendChild(noDomainsOption);
            console.warn('لم يتم العثور على أي ميادين في الصفحة');
        }
    }

    // إعادة تحميل قائمة العناصر الأب مع checkboxes
    function reloadParentOptions() {
        const parentContainer = document.getElementById('parent_checkboxes_container');
        const currentType = document.getElementById('multiple_entry_type').value;

        // مسح القائمة الحالية
        parentContainer.innerHTML = '';

        // تحديد نوع العنصر الأب المطلوب
        let parentType = '';
        if (currentType === 'domain') parentType = 'subject';
        else if (currentType === 'material') parentType = 'domain';
        else if (currentType === 'competency') parentType = 'material';

        if (!parentType) {
            parentContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>اختر ميدان من الخطوة 1 لعرض المواد المعرفية</p>
                </div>
            `;
            return;
        }

        // تحميل العناصر الأب من الصفحة
        const entries = document.querySelectorAll(`tr[data-type="${parentType}"]`);

        if (entries.length === 0) {
            parentContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                    <p>لا توجد عناصر متاحة من نوع ${parentType}</p>
                </div>
            `;
            return;
        }

        entries.forEach((entry, index) => {
            const entryId = entry.getAttribute('data-id');
            const entryName = entry.getAttribute('data-name');
            if (entryId && entryName) {
                const checkboxDiv = document.createElement('div');
                checkboxDiv.className = 'form-check mb-2 p-2 rounded parent-checkbox-item';
                checkboxDiv.setAttribute('data-parent-id', entryId);
                checkboxDiv.setAttribute('data-parent-name', entryName);

                checkboxDiv.innerHTML = `
                    <input class="form-check-input parent-checkbox" type="checkbox"
                           value="${entryId}" id="parent_${entryId}"
                           onchange="updateSelectedParents()">
                    <label class="form-check-label w-100" for="parent_${entryId}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold">${entryName}</span>
                            <small class="text-muted">ID: ${entryId}</small>
                        </div>
                    </label>
                `;

                parentContainer.appendChild(checkboxDiv);
            }
        });

        // تحديث عداد العناصر المحددة
        updateSelectedParents();

        console.log(`تم إعادة تحميل ${entries.length} عنصر أب من نوع ${parentType} مع checkboxes`);
    }

    // فلترة المواد المعرفية حسب الميدان المختار
    function filterByDomain() {
        const selectedDomainId = document.getElementById('domain_filter').value;
        const parentSelector = document.getElementById('parent_selector');

        console.log(`فلترة حسب الميدان: ${selectedDomainId || 'جميع الميادين'}`);

        // إعادة تحميل قائمة العناصر الأب أولاً
        reloadParentOptions();

        const checkboxItems = document.querySelectorAll('.parent-checkbox-item');
        let visibleCount = 0;

        console.log(`عدد العناصر للفحص: ${checkboxItems.length}`);

        // تطبيق الفلترة حسب الميدان المختار
        if (selectedDomainId && checkboxItems.length > 0) {
            // إذا تم اختيار ميدان، أظهر فقط المواد المعرفية المتعلقة به
            checkboxItems.forEach(item => {
                const parentId = item.getAttribute('data-parent-id');
                const materialEntry = document.querySelector(`tr[data-type="material"][data-id="${parentId}"]`);

                if (!materialEntry) {
                    console.log(`لم توجد مادة معرفية بالمعرف: ${parentId}`);
                    item.style.display = 'none';
                    return;
                }

                const materialParentId = materialEntry.getAttribute('data-parent-id');

                if (materialParentId === selectedDomainId) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });
        } else {
            // إذا لم يتم اختيار ميدان، أظهر جميع المواد المعرفية
            checkboxItems.forEach(item => {
                item.style.display = '';
                visibleCount++;
            });
        }

        // تطبيق البحث النصي أيضاً إذا كان موجوداً
        const searchTerm = document.getElementById('parent_search').value;
        if (searchTerm) {
            filterParentOptions();
        } else {
            updateFilteredCount(visibleCount, selectedDomainId !== '');
        }

        // تحديث النص التوضيحي
        updateDomainFilterInfo(selectedDomainId, visibleCount);

        console.log(`نتائج الفلترة: ${visibleCount} مادة معرفية مرئية`);
    }

    // مسح فلتر الميدان وإظهار جميع العناصر
    function clearDomainFilter() {
        document.getElementById('domain_filter').value = '';

        // إعادة تحميل جميع العناصر الأب
        reloadParentOptions();

        // إعادة تطبيق البحث النصي إذا كان موجوداً
        const searchTerm = document.getElementById('parent_search').value;
        if (searchTerm) {
            filterParentOptions();
        } else {
            // إظهار جميع العناصر
            const checkboxItems = document.querySelectorAll('.parent-checkbox-item');
            let visibleCount = checkboxItems.length;

            checkboxItems.forEach(item => {
                item.style.display = '';
            });

            updateFilteredCount(visibleCount, false);
            showNoResultsMessage(false);
        }

        document.getElementById('domain_filter').focus();
        console.log('تم مسح فلتر الميدان وإظهار جميع العناصر');
    }

    // تحديث النص التوضيحي لفلتر الميدان
    function updateDomainFilterInfo(selectedDomainId, visibleCount) {
        const domainFilter = document.getElementById('domain_filter');
        const selectedOption = domainFilter.querySelector(`option[value="${selectedDomainId}"]`);

        if (selectedDomainId && selectedOption) {
            const domainName = selectedOption.textContent;
            console.log(`تم فلترة ${visibleCount} مادة معرفية للميدان: ${domainName}`);
        }
    }

    // تصفية العناصر الأب بناءً على البحث (تعمل على النتائج المفلترة مسبقاً)
    function filterParentOptions() {
        const searchTerm = document.getElementById('parent_search').value.toLowerCase();
        const selectedDomainId = document.getElementById('domain_filter').value;
        const checkboxItems = document.querySelectorAll('.parent-checkbox-item');
        let visibleCount = 0;

        console.log(`البحث النصي: "${searchTerm}" في النتائج المفلترة`);

        checkboxItems.forEach(item => {
            const parentId = item.getAttribute('data-parent-id');
            const parentName = item.getAttribute('data-parent-name');
            let showItem = true;

            // أولاً: فحص فلتر الميدان (إذا كان مفعلاً)
            if (selectedDomainId) {
                const materialEntry = document.querySelector(`tr[data-type="material"][data-id="${parentId}"]`);
                if (materialEntry) {
                    const materialParentId = materialEntry.getAttribute('data-parent-id');
                    if (materialParentId !== selectedDomainId) {
                        showItem = false;
                    }
                } else {
                    showItem = false;
                }
            }

            // ثانياً: فحص البحث النصي (إذا كان موجوداً)
            if (showItem && searchTerm) {
                const itemText = parentName.toLowerCase();
                if (!itemText.includes(searchTerm)) {
                    showItem = false;
                }
            }

            if (showItem) {
                item.style.display = '';
                visibleCount++;

                // تمييز النص المطابق
                if (searchTerm) {
                    const label = item.querySelector('.form-check-label span');
                    if (label) {
                        const originalText = parentName;
                        const regex = new RegExp(`(${searchTerm})`, 'gi');
                        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                        label.innerHTML = highlightedText;
                    }
                }
            } else {
                item.style.display = 'none';
            }
        });

        // تحديث عداد النتائج
        const isFiltering = searchTerm !== '' || selectedDomainId !== '';
        updateFilteredCount(visibleCount, isFiltering);

        // إظهار/إخفاء رسالة عدم وجود نتائج
        showNoResultsMessage(visibleCount === 0 && isFiltering);

        console.log(`نتائج البحث النصي: ${visibleCount} عنصر مرئي`);
    }

    // دوال التحكم في checkboxes

    // تحديث قائمة العناصر المحددة
    function updateSelectedParents() {
        const checkboxes = document.querySelectorAll('.parent-checkbox:checked');
        const selectedIds = Array.from(checkboxes).map(cb => cb.value);

        // تحديث الحقل المخفي
        document.getElementById('selected_parents').value = selectedIds.join(',');

        // تحديث العداد
        const countElement = document.getElementById('selected_count');
        if (countElement) {
            countElement.textContent = selectedIds.length;
        }

        console.log(`تم تحديد ${selectedIds.length} عنصر: [${selectedIds.join(', ')}]`);
    }

    // تحديد جميع العناصر المرئية
    function selectAllParents() {
        const visibleCheckboxes = document.querySelectorAll('.parent-checkbox-item:not([style*="display: none"]) .parent-checkbox');
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        updateSelectedParents();
        console.log(`تم تحديد جميع العناصر المرئية (${visibleCheckboxes.length} عنصر)`);
    }

    // إلغاء تحديد جميع العناصر
    function clearAllParents() {
        const checkboxes = document.querySelectorAll('.parent-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateSelectedParents();
        console.log('تم إلغاء تحديد جميع العناصر');
    }

    // عكس التحديد للعناصر المرئية
    function toggleParentSelection() {
        const visibleCheckboxes = document.querySelectorAll('.parent-checkbox-item:not([style*="display: none"]) .parent-checkbox');
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = !checkbox.checked;
        });
        updateSelectedParents();
        console.log(`تم عكس التحديد للعناصر المرئية (${visibleCheckboxes.length} عنصر)`);
    }

    // مسح البحث النصي والعودة لنتائج فلتر الميدان
    function clearParentSearch() {
        document.getElementById('parent_search').value = '';
        const checkboxItems = document.querySelectorAll('.parent-checkbox-item');

        // إزالة التمييز من جميع العناصر
        checkboxItems.forEach(item => {
            const label = item.querySelector('.form-check-label span');
            if (label) {
                const parentName = item.getAttribute('data-parent-name');
                label.innerHTML = parentName;
            }
        });

        // إعادة تطبيق فلتر الميدان (إذا كان مفعلاً) أو إظهار جميع العناصر
        const selectedDomainId = document.getElementById('domain_filter').value;
        if (selectedDomainId) {
            // إعادة تطبيق فلتر الميدان فقط
            filterByDomain();
        } else {
            // إظهار جميع العناصر
            let visibleCount = 0;
            checkboxItems.forEach(item => {
                item.style.display = '';
                visibleCount++;
            });
            updateFilteredCount(visibleCount, false);
            showNoResultsMessage(false);
        }

        document.getElementById('parent_search').focus();
        console.log('تم مسح البحث النصي والعودة لنتائج فلتر الميدان');
    }

    // تحديث عداد العناصر الإجمالي
    function updateParentCount() {
        const checkboxItems = document.querySelectorAll('.parent-checkbox-item');
        const countElement = document.getElementById('parent_count');

        if (countElement) {
            countElement.textContent = checkboxItems.length;
        }
    }

    // تحديث عداد النتائج المفلترة
    function updateFilteredCount(filteredCount, isFiltering) {
        const filteredCountElement = document.getElementById('filtered_count');
        const filteredNumberElement = document.getElementById('filtered_number');
        const domainFilteredInfo = document.getElementById('domain_filtered_info');
        const filterStatus = document.getElementById('filter_status');
        const selectedDomainId = document.getElementById('domain_filter').value;

        // إظهار/إخفاء قسم حالة الفلترة
        const showFilterStatus = isFiltering || selectedDomainId;
        if (filterStatus) {
            filterStatus.style.display = showFilterStatus ? 'block' : 'none';
        }

        // تحديث عداد البحث النصي
        if (isFiltering && filteredCountElement && filteredNumberElement) {
            filteredNumberElement.textContent = filteredCount;
            filteredCountElement.style.display = '';
        } else if (filteredCountElement) {
            filteredCountElement.style.display = 'none';
        }

        // إظهار معلومات فلتر الميدان
        if (domainFilteredInfo) {
            if (selectedDomainId) {
                domainFilteredInfo.style.display = '';
            } else {
                domainFilteredInfo.style.display = 'none';
            }
        }
    }

    // إظهار/إخفاء رسالة عدم وجود نتائج
    function showNoResultsMessage(show) {
        const noResultsMessage = document.getElementById('no_results_message');
        if (noResultsMessage) {
            noResultsMessage.style.display = show ? 'block' : 'none';
        }
    }

    // تمييز النص المطابق في النتائج
    function highlightSearchTerm(searchTerm) {
        if (!searchTerm) return;

        const parentSelector = document.getElementById('parent_selector');
        const options = parentSelector.querySelectorAll('option[value!="0"]');

        options.forEach(option => {
            if (option.style.display !== 'none') {
                const originalText = option.textContent;
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                const highlightedText = originalText.replace(regex, '<mark>$1</mark>');

                // إنشاء عنصر مؤقت لتحويل HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = highlightedText;
                option.innerHTML = tempDiv.innerHTML;
            }
        });
    }

    // إضافة مستمع للضغط على Enter في خانة البحث
    document.addEventListener('DOMContentLoaded', function() {
        const parentSearch = document.getElementById('parent_search');
        if (parentSearch) {
            parentSearch.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    // اختيار أول نتيجة مرئية
                    const parentSelector = document.getElementById('parent_selector');
                    const visibleOptions = Array.from(parentSelector.querySelectorAll('option'))
                        .filter(option => option.style.display !== 'none' && option.value !== '0');

                    if (visibleOptions.length > 0) {
                        parentSelector.value = visibleOptions[0].value;
                        parentSelector.dispatchEvent(new Event('change'));
                    }
                }
            });

            // إضافة تأثير بصري عند التركيز
            parentSearch.addEventListener('focus', function() {
                this.parentElement.classList.add('shadow-sm');
            });

            parentSearch.addEventListener('blur', function() {
                this.parentElement.classList.remove('shadow-sm');
            });
        }
    });
</script>

<!-- CSS مخصص لتحسين مظهر البحث -->
<style>
    /* تحسين مظهر خانة البحث */
    #parent_search {
        border-radius: 0.375rem 0 0 0.375rem;
        transition: all 0.3s ease;
    }

    #parent_search:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    /* تحسين مظهر قائمة الاختيار */
    #parent_selector {
        border-radius: 0.375rem;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    #parent_selector:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    /* تحسين مظهر الخيارات */
    #parent_selector option {
        padding: 8px 12px;
        border-bottom: 1px solid #e9ecef;
    }

    #parent_selector option:hover {
        background-color: #f8f9fa;
    }

    #parent_selector option:checked {
        background-color: #0d6efd;
        color: white;
    }

    /* تمييز النص المطابق */
    mark {
        background-color: #fff3cd;
        color: #856404;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
    }

    /* تحسين مظهر الأزرار */
    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    /* تحسين مظهر النصوص المساعدة */
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* تحسين مظهر الأيقونات */
    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        color: #6c757d;
    }

    /* تأثيرات الحوم */
    .input-group:hover .input-group-text {
        background-color: #e9ecef;
        color: #495057;
    }

    /* تحسين مظهر العدادات */
    #parent_count, #filtered_number {
        font-weight: bold;
        color: #0d6efd;
    }

    /* تحسين التباعد */
    .mb-2 {
        margin-bottom: 0.75rem !important;
    }

    /* تحسين مظهر الحاوي */
    #parent_selector_container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
    }

    /* تحسين مظهر خانة البحث (الخطوة الثانية) */
    .mb-2:has(#parent_search) {
        background-color: #e3f2fd;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #bbdefb;
        margin-bottom: 1rem;
        position: relative;
    }

    /* إضافة شارة "الخطوة الثانية" */
    .mb-2:has(#parent_search)::before {
        content: "🥈";
        position: absolute;
        top: -10px;
        right: 15px;
        background: #2196f3;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    /* تحسين تسميات الخطوات */
    .form-label strong {
        color: #495057;
        font-size: 1.1rem;
    }

    #domain_filter_container .form-label strong {
        color: #856404;
    }

    .mb-2:has(#parent_search) .form-label strong {
        color: #1976d2;
    }

    /* تحسين مظهر حاوي فلتر الميدان (الأولوية الأولى) */
    #domain_filter_container {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        padding: 1.25rem;
        border-radius: 0.75rem;
        border: 2px solid #ffc107;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        position: relative;
    }

    /* إضافة شارة "الأولوية" */
    #domain_filter_container::before {
        content: "🥇";
        position: absolute;
        top: -10px;
        right: 15px;
        background: #ffc107;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    /* تحسين مظهر قائمة فلتر الميدان */
    #domain_filter {
        border-radius: 0 0.375rem 0.375rem 0;
        transition: all 0.3s ease;
    }

    #domain_filter:focus {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }

    /* تحسين مظهر زر مسح فلتر الميدان */
    .btn-outline-info {
        border-color: #0dcaf0;
        color: #0dcaf0;
        transition: all 0.3s ease;
    }

    .btn-outline-info:hover {
        background-color: #0dcaf0;
        border-color: #0dcaf0;
        color: white;
    }

    /* تحسين مظهر أيقونة الفلتر */
    .fa-layer-group {
        color: #ffc107;
    }

    .fa-filter {
        color: #fd7e14;
    }

    .fa-lightbulb {
        color: #ffc107;
    }

    /* تأثيرات خاصة لحاوي الفلتر */
    #domain_filter_container:hover {
        background-color: #fff8e1;
        border-color: #ffcc02;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
    }

    /* تحسين النصوص التوضيحية للفلتر */
    #domain_filter_container .form-text {
        color: #856404;
        font-weight: 500;
    }

    /* تحسين تسمية الفلتر */
    #domain_filter_container .form-label {
        color: #856404;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* تحسين مظهر checkboxes العناصر الأب */
    .parent-checkbox-item {
        transition: all 0.3s ease;
        border: 1px solid transparent;
        background-color: white;
    }

    .parent-checkbox-item:hover {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        transform: translateX(5px);
    }

    .parent-checkbox-item .form-check-input:checked + .form-check-label {
        color: #0d6efd;
        font-weight: 600;
    }

    .parent-checkbox-item .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    /* تحسين أزرار التحكم السريع */
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    /* تحسين عداد العناصر المحددة */
    #selected_count {
        font-weight: bold;
        color: #0d6efd;
        font-size: 1.1em;
    }

    /* تحسين حاوي checkboxes */
    #parent_checkboxes_container {
        max-height: 300px;
        overflow-y: auto;
    }

    #parent_checkboxes_container::-webkit-scrollbar {
        width: 8px;
    }

    #parent_checkboxes_container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    #parent_checkboxes_container::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    #parent_checkboxes_container::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    /* تحسين تمييز النص في checkboxes */
    .parent-checkbox-item mark {
        background-color: #fff3cd;
        color: #856404;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
    }

    /* تحسين الحالة الفارغة */
    .text-center.text-muted {
        padding: 2rem 1rem;
    }

    .text-center.text-muted .fa-2x {
        opacity: 0.5;
    }

    /* تأثير التركيز على الحاوي */
    .shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }
</style>

{% endblock %}