# ميزة الاختيار المتعدد للعناصر الأب - نظام Ta9affi v1.04

## 🎯 الهدف من الميزة

تم تطوير ميزة الاختيار المتعدد للعناصر الأب في صفحة "إضافة كفاءات متعددة" لتمكين المستخدم من تحديد عدة مواد معرفية لها نفس الكفاءات المستهدفة وإضافة الكفاءات لجميعها دفعة واحدة، مما يوفر الوقت والجهد بشكل كبير.

## 🚀 المميزات الجديدة

### **1. قائمة Checkboxes بدلاً من القائمة المنسدلة**
```html
<!-- قبل التطوير: قائمة اختيار واحد -->
<select class="form-select" id="parent_selector" name="parent_selector">
    <option value="0">اختر العنصر الأب</option>
</select>

<!-- بعد التطوير: قائمة checkboxes متعددة -->
<div id="parent_checkboxes_container" class="p-2">
    <div class="form-check mb-2 p-2 rounded parent-checkbox-item">
        <input class="form-check-input parent-checkbox" type="checkbox" 
               value="123" id="parent_123" onchange="updateSelectedParents()">
        <label class="form-check-label w-100" for="parent_123">
            <div class="d-flex justify-content-between align-items-center">
                <span class="fw-bold">اسم المادة المعرفية</span>
                <small class="text-muted">ID: 123</small>
            </div>
        </label>
    </div>
</div>
```

### **2. أزرار التحكم السريع**
```html
<div class="btn-group btn-group-sm" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="selectAllParents()">
        <i class="fas fa-check-double me-1"></i>
        تحديد الكل
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="clearAllParents()">
        <i class="fas fa-times me-1"></i>
        إلغاء الكل
    </button>
    <button type="button" class="btn btn-outline-info" onclick="toggleParentSelection()">
        <i class="fas fa-exchange-alt me-1"></i>
        عكس التحديد
    </button>
</div>
```

### **3. عداد العناصر المحددة**
```html
<span class="ms-3 text-muted small">
    <span id="selected_count">0</span> عنصر محدد
</span>
```

### **4. حقل مخفي لتخزين الاختيارات**
```html
<input type="hidden" id="selected_parents" name="selected_parents" value="">
```

## ⚡ الوظائف التفاعلية الجديدة

### **1. تحديث قائمة العناصر المحددة**
```javascript
function updateSelectedParents() {
    const checkboxes = document.querySelectorAll('.parent-checkbox:checked');
    const selectedIds = Array.from(checkboxes).map(cb => cb.value);
    
    // تحديث الحقل المخفي
    document.getElementById('selected_parents').value = selectedIds.join(',');
    
    // تحديث العداد
    const countElement = document.getElementById('selected_count');
    if (countElement) {
        countElement.textContent = selectedIds.length;
    }
    
    console.log(`تم تحديد ${selectedIds.length} عنصر: [${selectedIds.join(', ')}]`);
}
```

### **2. تحديد جميع العناصر المرئية**
```javascript
function selectAllParents() {
    const visibleCheckboxes = document.querySelectorAll('.parent-checkbox-item:not([style*="display: none"]) .parent-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedParents();
    console.log(`تم تحديد جميع العناصر المرئية (${visibleCheckboxes.length} عنصر)`);
}
```

### **3. إلغاء تحديد جميع العناصر**
```javascript
function clearAllParents() {
    const checkboxes = document.querySelectorAll('.parent-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedParents();
    console.log('تم إلغاء تحديد جميع العناصر');
}
```

### **4. عكس التحديد للعناصر المرئية**
```javascript
function toggleParentSelection() {
    const visibleCheckboxes = document.querySelectorAll('.parent-checkbox-item:not([style*="display: none"]) .parent-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = !checkbox.checked;
    });
    updateSelectedParents();
    console.log(`تم عكس التحديد للعناصر المرئية (${visibleCheckboxes.length} عنصر)`);
}
```

## 🔄 معالجة الإرسال المتعدد

### **التحقق من الاختيارات**
```javascript
const selectedParents = document.getElementById('selected_parents').value;

// التحقق من وجود كفاءات للإضافة
if (entries.length === 0) {
    alert('الرجاء إدخال كفاءة واحدة على الأقل');
    return;
}

// التحقق من تحديد عناصر أب
if (!selectedParents || selectedParents.trim() === '') {
    alert('الرجاء تحديد عنصر أب واحد على الأقل من الخطوة 3');
    return;
}
```

### **تأكيد العملية**
```javascript
const selectedParentIds = selectedParents.split(',').filter(id => id.trim() !== '');

const confirmMessage = `هل أنت متأكد من إضافة ${entries.length} كفاءة إلى ${selectedParentIds.length} مادة معرفية؟\n\nسيتم إنشاء ${entries.length * selectedParentIds.length} كفاءة إجمالية.`;

if (!confirm(confirmMessage)) {
    return;
}
```

### **الإرسال المتوازي**
```javascript
selectedParentIds.forEach((parentId, index) => {
    // إنشاء FormData جديد لكل عنصر أب
    const individualFormData = new FormData();
    individualFormData.append('entry_type', formData.get('entry_type'));
    individualFormData.append('parent_id', parentId.trim());
    individualFormData.append('multiple_entries', entriesText);
    individualFormData.append('is_multiple', 'true');
    individualFormData.append('is_active', formData.get('is_active') || 'on');

    // إرسال الطلب
    fetch(this.action, {
        method: 'POST',
        body: individualFormData
    })
    .then(response => response.json())
    .then(data => {
        completedRequests++;
        
        if (data.success) {
            successCount++;
            console.log(`تم إضافة الكفاءات للعنصر الأب ${parentId} بنجاح`);
        } else {
            errorMessages.push(`العنصر ${parentId}: ${data.message}`);
        }

        // معالجة النتائج النهائية
        if (completedRequests === totalRequests) {
            // عرض النتائج وإعادة تحميل الصفحة
        }
    });
});
```

## 🎨 التحسينات البصرية

### **1. تصميم Checkboxes محسن**
```css
.parent-checkbox-item {
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background-color: white;
}

.parent-checkbox-item:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    transform: translateX(5px);
}

.parent-checkbox-item .form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 600;
}
```

### **2. أزرار التحكم السريع**
```css
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}
```

### **3. عداد محسن**
```css
#selected_count {
    font-weight: bold;
    color: #0d6efd;
    font-size: 1.1em;
}
```

### **4. تمرير محسن**
```css
#parent_checkboxes_container::-webkit-scrollbar {
    width: 8px;
}

#parent_checkboxes_container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}
```

## 🔧 كيفية الاستخدام

### **الخطوات:**
1. **اذهب إلى**: `http://127.0.0.1:5000/admin/databases`
2. **اختر قاعدة بيانات** تحتوي على ميادين ومواد معرفية
3. **انقر على تبويب** "الكفاءات المستهدفة"
4. **انقر على زر** "إضافة متعددة"

### **استخدام الميزة الجديدة:**

#### **🥇 الخطوة 1: فلترة الميدان**
- اختر ميدان (مثل "فهم المنطوق")
- ستظهر المواد المعرفية المتعلقة بالميدان فوراً

#### **🥈 الخطوة 2: البحث النصي (اختياري)**
- ابحث عن مواد معرفية محددة داخل الميدان

#### **📋 الخطوة 3: اختيار العناصر الأب (متعدد)**
- **تحديد فردي**: انقر على checkbox بجانب كل مادة معرفية
- **تحديد الكل**: انقر على "تحديد الكل" لتحديد جميع المواد المرئية
- **إلغاء الكل**: انقر على "إلغاء الكل" لإلغاء جميع التحديدات
- **عكس التحديد**: انقر على "عكس التحديد" لعكس الاختيارات الحالية

#### **📝 الخطوة 4: إدخال الكفاءات**
- أدخل الكفاءات المطلوبة (كفاءة في كل سطر)

#### **🚀 الخطوة 5: الإرسال**
- انقر على "إضافة"
- ستظهر رسالة تأكيد تعرض عدد الكفاءات والمواد المعرفية
- بعد التأكيد، سيتم إضافة الكفاءات لجميع المواد المحددة

## 🎯 مثال عملي

### **السيناريو:** إضافة كفاءات "فهم المنطوق" لعدة مواد معرفية

#### **الخطوات:**
1. **فلترة الميدان**: اختر "فهم المنطوق"
   - **النتيجة**: ظهور 5 مواد معرفية (حوار، قصة، نص إعلامي، أنشودة، خطبة)

2. **تحديد المواد**: حدد 3 مواد (حوار، قصة، نص إعلامي)
   - **العداد**: "3 عنصر محدد"

3. **إدخال الكفاءات**: أدخل 4 كفاءات مختلفة

4. **الإرسال**: انقر على "إضافة"
   - **رسالة التأكيد**: "هل أنت متأكد من إضافة 4 كفاءة إلى 3 مادة معرفية؟ سيتم إنشاء 12 كفاءة إجمالية."

5. **النتيجة**: إضافة 12 كفاءة (4 كفاءات × 3 مواد معرفية)

## ✅ الفوائد المحققة

### **قبل الميزة:**
- ❌ إضافة الكفاءات لمادة معرفية واحدة فقط في كل مرة
- ❌ تكرار العملية لكل مادة معرفية منفصلة
- ❌ وقت طويل لإضافة نفس الكفاءات لعدة مواد

### **بعد الميزة:**
- ✅ **إضافة متعددة**: كفاءات لعدة مواد معرفية دفعة واحدة
- ✅ **توفير وقت هائل**: من ساعات إلى دقائق
- ✅ **تقليل الأخطاء**: إدخال الكفاءات مرة واحدة فقط
- ✅ **سهولة الاستخدام**: واجهة بديهية مع أزرار تحكم سريع
- ✅ **مرونة عالية**: اختيار دقيق للمواد المطلوبة

### **مقارنة الأداء:**
- **الطريقة القديمة**: 5 مواد × 4 كفاءات = 20 عملية منفصلة
- **الطريقة الجديدة**: عملية واحدة تنشئ 20 كفاءة تلقائياً

## 🔍 نصائح للاستخدام الأمثل

### **1. استخدم الفلترة أولاً**
- اختر الميدان المناسب قبل التحديد
- استخدم البحث النصي للتصفية الدقيقة

### **2. استخدم أزرار التحكم السريع**
- "تحديد الكل" للمواد المتشابهة
- "عكس التحديد" لتغيير الاختيارات بسرعة

### **3. تحقق من العداد**
- راقب عدد العناصر المحددة
- تأكد من صحة الاختيارات قبل الإرسال

### **4. استخدم رسالة التأكيد**
- راجع الأرقام في رسالة التأكيد
- تأكد من صحة عدد الكفاءات والمواد

هذه الميزة تحول عملية إضافة الكفاءات من مهمة مملة ومتكررة إلى عملية سريعة وفعالة! 🚀
